import React, { useState } from 'react';

const Settings = () => {
  const [settings, setSettings] = useState({
    theme: 'light',
    notifications: true,
    autoSave: true,
    language: 'en',
    fontSize: 'medium',
    showTips: true
  });

  const [userProfile, setUserProfile] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Administrator',
    avatar: ''
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
    
    // Simulate saving to local storage
    localStorage.setItem('appSettings', JSON.stringify({
      ...settings,
      [key]: value
    }));
  };

  const handleProfileChange = (key, value) => {
    setUserProfile(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'app-settings.json';
    link.click();
  };

  return (
    <div className="settings fade-in">
      <div className="page-header" style={{ marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '2rem', color: '#2d3748', marginBottom: '0.5rem' }}>
          Settings ⚙️
        </h1>
        <p style={{ color: '#718096', fontSize: '1.1rem' }}>
          Customize your application preferences and profile settings.
        </p>
      </div>

      <div className="grid grid-2">
        {/* User Profile */}
        <div className="card">
          <h2>User Profile</h2>
          <div className="form-group">
            <label className="form-label">Full Name</label>
            <input
              type="text"
              className="form-input"
              value={userProfile.name}
              onChange={(e) => handleProfileChange('name', e.target.value)}
            />
          </div>
          <div className="form-group">
            <label className="form-label">Email Address</label>
            <input
              type="email"
              className="form-input"
              value={userProfile.email}
              onChange={(e) => handleProfileChange('email', e.target.value)}
            />
          </div>
          <div className="form-group">
            <label className="form-label">Role</label>
            <select
              className="form-input"
              value={userProfile.role}
              onChange={(e) => handleProfileChange('role', e.target.value)}
            >
              <option value="Administrator">Administrator</option>
              <option value="Manager">Manager</option>
              <option value="User">User</option>
              <option value="Guest">Guest</option>
            </select>
          </div>
          <button className="btn btn-primary">
            Update Profile
          </button>
        </div>

        {/* Application Settings */}
        <div className="card">
          <h2>Application Settings</h2>
          
          <div className="form-group">
            <label className="form-label">Theme</label>
            <select
              className="form-input"
              value={settings.theme}
              onChange={(e) => handleSettingChange('theme', e.target.value)}
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Language</label>
            <select
              className="form-input"
              value={settings.language}
              onChange={(e) => handleSettingChange('language', e.target.value)}
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Font Size</label>
            <select
              className="form-input"
              value={settings.fontSize}
              onChange={(e) => handleSettingChange('fontSize', e.target.value)}
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>

          <div className="form-group">
            <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={settings.notifications}
                onChange={(e) => handleSettingChange('notifications', e.target.checked)}
              />
              Enable Notifications
            </label>
          </div>

          <div className="form-group">
            <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={settings.autoSave}
                onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
              />
              Auto-save Changes
            </label>
          </div>

          <div className="form-group">
            <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={settings.showTips}
                onChange={(e) => handleSettingChange('showTips', e.target.checked)}
              />
              Show Tips and Hints
            </label>
          </div>
        </div>
      </div>

      {/* Advanced Settings */}
      <div className="card">
        <h2>Advanced Settings</h2>
        <div className="grid grid-3">
          <div>
            <h3>Data Management</h3>
            <p>Manage your application data and preferences.</p>
            <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
              <button className="btn btn-secondary" onClick={exportSettings}>
                Export Settings
              </button>
              <button className="btn btn-secondary">
                Import Settings
              </button>
            </div>
          </div>
          
          <div>
            <h3>Performance</h3>
            <p>Optimize application performance and resource usage.</p>
            <div style={{ marginTop: '1rem' }}>
              <button className="btn btn-secondary">
                Clear Cache
              </button>
            </div>
          </div>
          
          <div>
            <h3>Security</h3>
            <p>Configure security and privacy settings.</p>
            <div style={{ marginTop: '1rem' }}>
              <button className="btn btn-secondary">
                Change Password
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Current Settings Display */}
      <div className="card">
        <h2>Current Configuration</h2>
        <pre style={{
          background: 'rgba(0,0,0,0.05)',
          padding: '1rem',
          borderRadius: '8px',
          fontSize: '0.9rem',
          overflow: 'auto'
        }}>
          {JSON.stringify(settings, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default Settings;
