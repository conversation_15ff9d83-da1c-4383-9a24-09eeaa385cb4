import React, { useState } from 'react';

const Projects = () => {
  const [projects, setProjects] = useState([
    {
      id: 1,
      name: 'Website Redesign',
      description: 'Complete overhaul of the company website with modern design',
      status: 'In Progress',
      progress: 75,
      team: ['<PERSON>', '<PERSON>', '<PERSON>'],
      deadline: '2024-02-15',
      priority: 'High'
    },
    {
      id: 2,
      name: 'Mobile App Development',
      description: 'Native mobile application for iOS and Android platforms',
      status: 'Planning',
      progress: 25,
      team: ['<PERSON>', '<PERSON>'],
      deadline: '2024-03-30',
      priority: 'Medium'
    },
    {
      id: 3,
      name: 'API Integration',
      description: 'Integration with third-party APIs for enhanced functionality',
      status: 'Completed',
      progress: 100,
      team: ['<PERSON>', '<PERSON>'],
      deadline: '2024-01-20',
      priority: 'High'
    },
    {
      id: 4,
      name: 'Database Migration',
      description: 'Migration from legacy database to modern cloud solution',
      status: 'On Hold',
      progress: 40,
      team: ['<PERSON>', '<PERSON>'],
      deadline: '2024-04-10',
      priority: 'Low'
    }
  ]);

  const [showModal, setShowModal] = useState(false);
  const [newProject, setNewProject] = useState({
    name: '',
    description: '',
    deadline: '',
    priority: 'Medium'
  });

  const getStatusColor = (status) => {
    const colors = {
      'In Progress': '#667eea',
      'Planning': '#ed8936',
      'Completed': '#48bb78',
      'On Hold': '#a0aec0'
    };
    return colors[status] || '#667eea';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'High': '#e53e3e',
      'Medium': '#ed8936',
      'Low': '#48bb78'
    };
    return colors[priority] || '#667eea';
  };

  const handleCreateProject = () => {
    if (newProject.name && newProject.description) {
      const project = {
        id: projects.length + 1,
        ...newProject,
        status: 'Planning',
        progress: 0,
        team: []
      };
      setProjects([...projects, project]);
      setNewProject({ name: '', description: '', deadline: '', priority: 'Medium' });
      setShowModal(false);
    }
  };

  return (
    <div className="projects fade-in">
      <div className="page-header" style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '2rem' 
      }}>
        <div>
          <h1 style={{ fontSize: '2rem', color: '#2d3748', marginBottom: '0.5rem' }}>
            Projects 📁
          </h1>
          <p style={{ color: '#718096', fontSize: '1.1rem' }}>
            Manage and track all your active projects.
          </p>
        </div>
        <button 
          className="btn btn-primary"
          onClick={() => setShowModal(true)}
        >
          + New Project
        </button>
      </div>

      {/* Project Stats */}
      <div className="stats-grid" style={{ marginBottom: '2rem' }}>
        <div className="stat-card">
          <span className="stat-number">{projects.length}</span>
          <span className="stat-label">Total Projects</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">
            {projects.filter(p => p.status === 'In Progress').length}
          </span>
          <span className="stat-label">Active</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">
            {projects.filter(p => p.status === 'Completed').length}
          </span>
          <span className="stat-label">Completed</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">
            {Math.round(projects.reduce((acc, p) => acc + p.progress, 0) / projects.length)}%
          </span>
          <span className="stat-label">Avg Progress</span>
        </div>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-2">
        {projects.map((project) => (
          <div key={project.id} className="card">
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'flex-start',
              marginBottom: '1rem' 
            }}>
              <h3 style={{ margin: 0, flex: 1 }}>{project.name}</h3>
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                <span style={{
                  padding: '0.25rem 0.75rem',
                  borderRadius: '12px',
                  fontSize: '0.8rem',
                  fontWeight: '500',
                  background: getStatusColor(project.status),
                  color: 'white'
                }}>
                  {project.status}
                </span>
                <span style={{
                  padding: '0.25rem 0.75rem',
                  borderRadius: '12px',
                  fontSize: '0.8rem',
                  fontWeight: '500',
                  background: getPriorityColor(project.priority),
                  color: 'white'
                }}>
                  {project.priority}
                </span>
              </div>
            </div>

            <p style={{ marginBottom: '1rem', color: '#718096' }}>
              {project.description}
            </p>

            {/* Progress Bar */}
            <div style={{ marginBottom: '1rem' }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                marginBottom: '0.5rem' 
              }}>
                <span style={{ fontSize: '0.9rem', fontWeight: '500' }}>Progress</span>
                <span style={{ fontSize: '0.9rem', color: '#667eea' }}>
                  {project.progress}%
                </span>
              </div>
              <div style={{
                width: '100%',
                height: '8px',
                background: 'rgba(0,0,0,0.1)',
                borderRadius: '4px',
                overflow: 'hidden'
              }}>
                <div style={{
                  width: `${project.progress}%`,
                  height: '100%',
                  background: getStatusColor(project.status),
                  transition: 'width 0.3s ease'
                }} />
              </div>
            </div>

            {/* Team and Deadline */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              fontSize: '0.9rem',
              color: '#718096'
            }}>
              <div>
                <strong>Team:</strong> {project.team.join(', ') || 'Not assigned'}
              </div>
              <div>
                <strong>Due:</strong> {new Date(project.deadline).toLocaleDateString()}
              </div>
            </div>

            <div style={{ 
              display: 'flex', 
              gap: '0.5rem', 
              marginTop: '1rem' 
            }}>
              <button className="btn btn-secondary" style={{ flex: 1 }}>
                View Details
              </button>
              <button className="btn btn-primary" style={{ flex: 1 }}>
                Edit Project
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Create Project Modal */}
      {showModal && (
        <div className="modal-overlay" onClick={() => setShowModal(false)}>
          <div className="modal" onClick={(e) => e.stopPropagation()}>
            <h2 style={{ marginBottom: '1.5rem' }}>Create New Project</h2>
            
            <div className="form-group">
              <label className="form-label">Project Name</label>
              <input
                type="text"
                className="form-input"
                value={newProject.name}
                onChange={(e) => setNewProject({...newProject, name: e.target.value})}
                placeholder="Enter project name"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Description</label>
              <textarea
                className="form-input"
                rows="3"
                value={newProject.description}
                onChange={(e) => setNewProject({...newProject, description: e.target.value})}
                placeholder="Describe your project"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Deadline</label>
              <input
                type="date"
                className="form-input"
                value={newProject.deadline}
                onChange={(e) => setNewProject({...newProject, deadline: e.target.value})}
              />
            </div>

            <div className="form-group">
              <label className="form-label">Priority</label>
              <select
                className="form-input"
                value={newProject.priority}
                onChange={(e) => setNewProject({...newProject, priority: e.target.value})}
              >
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
              </select>
            </div>

            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>
              <button 
                className="btn btn-secondary" 
                style={{ flex: 1 }}
                onClick={() => setShowModal(false)}
              >
                Cancel
              </button>
              <button 
                className="btn btn-primary" 
                style={{ flex: 1 }}
                onClick={handleCreateProject}
              >
                Create Project
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Projects;
