import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';

// Handle Electron menu events
const { ipc<PERSON><PERSON><PERSON> } = window.require ? window.require('electron') : { ipcRenderer: null };

if (ipcRenderer) {
  // Listen for navigation events from main process
  ipcRenderer.on('navigate-to', (event, route) => {
    // This will be handled by the App component
    window.dispatchEvent(new CustomEvent('electron-navigate', { detail: route }));
  });

  // Listen for menu actions
  ipcRenderer.on('menu-new-project', () => {
    window.dispatchEvent(new CustomEvent('electron-new-project'));
  });

  ipcRenderer.on('menu-open-project', () => {
    window.dispatchEvent(new CustomEvent('electron-open-project'));
  });
}

ReactDOM.render(<App />, document.getElementById('root'));