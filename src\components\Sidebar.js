import React from 'react';
import { NavLink } from 'react-router-dom';

const Sidebar = ({ isOpen, onClose }) => {
  const navigationItems = [
    { path: '/', label: 'Dashboard', icon: '📊' },
    { path: '/analytics', label: 'Analytics', icon: '📈' },
    { path: '/projects', label: 'Projects', icon: '📁' },
    { path: '/tasks', label: 'Tasks', icon: '✅' },
    { path: '/calendar', label: 'Calendar', icon: '📅' },
    { path: '/messages', label: 'Messages', icon: '💬' },
    { path: '/settings', label: 'Settings', icon: '⚙️' },
    { path: '/about', label: 'About', icon: 'ℹ️' }
  ];

  return (
    <aside className={`sidebar ${isOpen ? 'open' : ''}`}>
      <nav>
        <ul className="sidebar-nav">
          {navigationItems.map((item) => (
            <li key={item.path}>
              <NavLink
                to={item.path}
                className={({ isActive }) => isActive ? 'active' : ''}
                onClick={onClose}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  fontSize: '0.95rem'
                }}
              >
                <span style={{ fontSize: '1.2rem' }}>{item.icon}</span>
                {item.label}
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
      
      <div style={{ 
        position: 'absolute', 
        bottom: '2rem', 
        left: '2rem', 
        right: '2rem',
        padding: '1rem',
        background: 'rgba(102, 126, 234, 0.1)',
        borderRadius: '8px',
        fontSize: '0.8rem',
        color: '#667eea'
      }}>
        <div style={{ fontWeight: '600', marginBottom: '0.5rem' }}>
          💡 Pro Tip
        </div>
        <div>
          Use Ctrl+Shift+D to quickly access the dashboard
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
