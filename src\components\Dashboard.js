import React, { useState, useEffect } from 'react';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalProjects: 12,
    completedTasks: 48,
    activeUsers: 156,
    revenue: 24500
  });

  const [recentActivities, setRecentActivities] = useState([
    { id: 1, action: 'New project created', time: '2 minutes ago', type: 'project' },
    { id: 2, action: 'Task completed by <PERSON>', time: '15 minutes ago', type: 'task' },
    { id: 3, action: 'New user registered', time: '1 hour ago', type: 'user' },
    { id: 4, action: 'Payment received', time: '2 hours ago', type: 'payment' },
    { id: 5, action: 'System backup completed', time: '3 hours ago', type: 'system' }
  ]);

  const [quickActions] = useState([
    { title: 'Create New Project', description: 'Start a new project with templates', icon: '🚀' },
    { title: 'Add Team Member', description: 'Invite new users to your workspace', icon: '👥' },
    { title: 'Generate Report', description: 'Create detailed analytics reports', icon: '📊' },
    { title: 'Schedule Meeting', description: 'Set up team meetings and calls', icon: '📅' }
  ]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prevStats => ({
        ...prevStats,
        activeUsers: prevStats.activeUsers + Math.floor(Math.random() * 3) - 1,
        revenue: prevStats.revenue + Math.floor(Math.random() * 100)
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getActivityIcon = (type) => {
    const icons = {
      project: '📁',
      task: '✅',
      user: '👤',
      payment: '💰',
      system: '⚙️'
    };
    return icons[type] || '📝';
  };

  return (
    <div className="dashboard fade-in">
      <div className="page-header" style={{ marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '2rem', color: '#2d3748', marginBottom: '0.5rem' }}>
          Welcome back! 👋
        </h1>
        <p style={{ color: '#718096', fontSize: '1.1rem' }}>
          Here's what's happening with your projects today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="stats-grid">
        <div className="stat-card">
          <span className="stat-number">{stats.totalProjects}</span>
          <span className="stat-label">Total Projects</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{stats.completedTasks}</span>
          <span className="stat-label">Completed Tasks</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">{stats.activeUsers}</span>
          <span className="stat-label">Active Users</span>
        </div>
        <div className="stat-card">
          <span className="stat-number">${stats.revenue.toLocaleString()}</span>
          <span className="stat-label">Revenue</span>
        </div>
      </div>

      <div className="grid grid-2">
        {/* Recent Activities */}
        <div className="card">
          <h2>Recent Activities</h2>
          <div className="activity-list">
            {recentActivities.map((activity) => (
              <div 
                key={activity.id}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  padding: '0.75rem 0',
                  borderBottom: '1px solid rgba(0,0,0,0.1)'
                }}
              >
                <span style={{ fontSize: '1.5rem' }}>
                  {getActivityIcon(activity.type)}
                </span>
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: '500', color: '#2d3748' }}>
                    {activity.action}
                  </div>
                  <div style={{ fontSize: '0.8rem', color: '#718096' }}>
                    {activity.time}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h2>Quick Actions</h2>
          <div className="grid" style={{ gap: '1rem' }}>
            {quickActions.map((action, index) => (
              <div 
                key={index}
                className="quick-action-item"
                style={{
                  padding: '1rem',
                  border: '2px solid rgba(102, 126, 234, 0.2)',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.target.style.borderColor = '#667eea';
                  e.target.style.background = 'rgba(102, 126, 234, 0.05)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.borderColor = 'rgba(102, 126, 234, 0.2)';
                  e.target.style.background = 'transparent';
                }}
              >
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                  {action.icon}
                </div>
                <h3 style={{ fontSize: '1rem', marginBottom: '0.25rem' }}>
                  {action.title}
                </h3>
                <p style={{ fontSize: '0.8rem', margin: 0 }}>
                  {action.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Progress Section */}
      <div className="card">
        <h2>Project Progress</h2>
        <div className="grid grid-3">
          {[
            { name: 'Website Redesign', progress: 75, color: '#667eea' },
            { name: 'Mobile App', progress: 45, color: '#48bb78' },
            { name: 'API Development', progress: 90, color: '#ed8936' }
          ].map((project, index) => (
            <div key={index} style={{ marginBottom: '1rem' }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                marginBottom: '0.5rem' 
              }}>
                <span style={{ fontWeight: '500' }}>{project.name}</span>
                <span style={{ color: project.color }}>{project.progress}%</span>
              </div>
              <div style={{
                width: '100%',
                height: '8px',
                background: 'rgba(0,0,0,0.1)',
                borderRadius: '4px',
                overflow: 'hidden'
              }}>
                <div style={{
                  width: `${project.progress}%`,
                  height: '100%',
                  background: project.color,
                  transition: 'width 0.3s ease'
                }} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
