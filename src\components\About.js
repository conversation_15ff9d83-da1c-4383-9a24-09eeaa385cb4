import React, { useState, useEffect } from 'react';

const About = () => {
  const [systemInfo, setSystemInfo] = useState({
    appVersion: '1.0.0',
    electronVersion: '36.5.0',
    nodeVersion: process.versions?.node || 'Unknown',
    chromeVersion: process.versions?.chrome || 'Unknown',
    platform: process.platform || 'Unknown',
    arch: process.arch || 'Unknown'
  });

  const [features] = useState([
    {
      title: 'Modern UI/UX',
      description: 'Beautiful, responsive design with smooth animations and glassmorphism effects.',
      icon: '🎨'
    },
    {
      title: 'Real-time Updates',
      description: 'Live data updates and real-time synchronization across all components.',
      icon: '⚡'
    },
    {
      title: 'Cross-platform',
      description: 'Runs seamlessly on Windows, macOS, and Linux operating systems.',
      icon: '🌐'
    },
    {
      title: 'Secure & Private',
      description: 'Built with security best practices and local data storage.',
      icon: '🔒'
    },
    {
      title: 'Customizable',
      description: 'Extensive settings and configuration options to match your workflow.',
      icon: '⚙️'
    },
    {
      title: 'Performance Optimized',
      description: 'Efficient resource usage and fast loading times.',
      icon: '🚀'
    }
  ]);

  const [teamMembers] = useState([
    {
      name: 'Development Team',
      role: 'Full-stack Development',
      description: 'Responsible for application architecture, frontend, and backend development.',
      avatar: '👨‍💻'
    },
    {
      name: 'Design Team',
      role: 'UI/UX Design',
      description: 'Creates beautiful and intuitive user interfaces and experiences.',
      avatar: '🎨'
    },
    {
      name: 'QA Team',
      role: 'Quality Assurance',
      description: 'Ensures application quality through comprehensive testing.',
      avatar: '🔍'
    }
  ]);

  const [stats] = useState([
    { label: 'Lines of Code', value: '2,500+' },
    { label: 'Components', value: '15+' },
    { label: 'Features', value: '25+' },
    { label: 'Tests', value: '100+' }
  ]);

  return (
    <div className="about fade-in">
      <div className="page-header" style={{ marginBottom: '2rem', textAlign: 'center' }}>
        <h1 style={{ fontSize: '2.5rem', color: '#2d3748', marginBottom: '0.5rem' }}>
          About This Application ℹ️
        </h1>
        <p style={{ color: '#718096', fontSize: '1.2rem', maxWidth: '600px', margin: '0 auto' }}>
          A modern Electron React application showcasing best practices in desktop app development.
        </p>
      </div>

      {/* App Info */}
      <div className="card" style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>⚡</div>
        <h2>Electron React Dashboard</h2>
        <p style={{ fontSize: '1.1rem', marginBottom: '2rem' }}>
          A powerful desktop application built with modern web technologies, 
          combining the flexibility of web development with native desktop capabilities.
        </p>
        
        <div className="stats-grid" style={{ maxWidth: '600px', margin: '0 auto' }}>
          {stats.map((stat, index) => (
            <div key={index} className="stat-card">
              <span className="stat-number" style={{ fontSize: '1.5rem' }}>{stat.value}</span>
              <span className="stat-label">{stat.label}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Features */}
      <div className="card">
        <h2 style={{ textAlign: 'center', marginBottom: '2rem' }}>Key Features</h2>
        <div className="grid grid-3">
          {features.map((feature, index) => (
            <div 
              key={index}
              style={{
                textAlign: 'center',
                padding: '1.5rem',
                border: '2px solid rgba(102, 126, 234, 0.1)',
                borderRadius: '12px',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#667eea';
                e.currentTarget.style.transform = 'translateY(-5px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = 'rgba(102, 126, 234, 0.1)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>
                {feature.icon}
              </div>
              <h3 style={{ marginBottom: '0.5rem' }}>{feature.title}</h3>
              <p style={{ fontSize: '0.9rem', margin: 0 }}>{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-2">
        {/* System Information */}
        <div className="card">
          <h2>System Information</h2>
          <div style={{ display: 'grid', gap: '1rem' }}>
            {Object.entries(systemInfo).map(([key, value]) => (
              <div 
                key={key}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  padding: '0.75rem',
                  background: 'rgba(102, 126, 234, 0.05)',
                  borderRadius: '8px'
                }}
              >
                <span style={{ fontWeight: '500', textTransform: 'capitalize' }}>
                  {key.replace(/([A-Z])/g, ' $1').trim()}:
                </span>
                <span style={{ color: '#667eea', fontFamily: 'monospace' }}>
                  {value}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Team */}
        <div className="card">
          <h2>Development Team</h2>
          <div style={{ display: 'grid', gap: '1rem' }}>
            {teamMembers.map((member, index) => (
              <div 
                key={index}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  padding: '1rem',
                  background: 'rgba(102, 126, 234, 0.05)',
                  borderRadius: '8px'
                }}
              >
                <div style={{ fontSize: '2rem' }}>{member.avatar}</div>
                <div>
                  <h3 style={{ margin: '0 0 0.25rem 0', fontSize: '1.1rem' }}>
                    {member.name}
                  </h3>
                  <div style={{ 
                    color: '#667eea', 
                    fontSize: '0.9rem', 
                    fontWeight: '500',
                    marginBottom: '0.25rem'
                  }}>
                    {member.role}
                  </div>
                  <p style={{ margin: 0, fontSize: '0.8rem', color: '#718096' }}>
                    {member.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Technology Stack */}
      <div className="card">
        <h2 style={{ textAlign: 'center', marginBottom: '2rem' }}>Technology Stack</h2>
        <div className="grid grid-2">
          <div>
            <h3>Frontend Technologies</h3>
            <ul style={{ listStyle: 'none', padding: 0 }}>
              {[
                { name: 'React 19', description: 'Modern UI library with hooks' },
                { name: 'React Router', description: 'Client-side routing' },
                { name: 'CSS3', description: 'Modern styling with animations' },
                { name: 'Webpack', description: 'Module bundling and optimization' }
              ].map((tech, index) => (
                <li key={index} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  padding: '0.5rem 0',
                  borderBottom: '1px solid rgba(0,0,0,0.1)'
                }}>
                  <span style={{ fontWeight: '500' }}>{tech.name}</span>
                  <span style={{ color: '#718096', fontSize: '0.9rem' }}>{tech.description}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div>
            <h3>Desktop Technologies</h3>
            <ul style={{ listStyle: 'none', padding: 0 }}>
              {[
                { name: 'Electron', description: 'Cross-platform desktop apps' },
                { name: 'Node.js', description: 'JavaScript runtime' },
                { name: 'Chromium', description: 'Web rendering engine' },
                { name: 'Native APIs', description: 'OS integration capabilities' }
              ].map((tech, index) => (
                <li key={index} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  padding: '0.5rem 0',
                  borderBottom: '1px solid rgba(0,0,0,0.1)'
                }}>
                  <span style={{ fontWeight: '500' }}>{tech.name}</span>
                  <span style={{ color: '#718096', fontSize: '0.9rem' }}>{tech.description}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div style={{ 
        textAlign: 'center', 
        padding: '2rem',
        color: '#718096',
        fontSize: '0.9rem'
      }}>
        <p>
          Built with ❤️ using modern web technologies.<br />
          © 2024 Electron React Dashboard. All rights reserved.
        </p>
      </div>
    </div>
  );
};

export default About;
