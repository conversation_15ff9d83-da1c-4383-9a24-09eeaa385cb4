# Electron React Dashboard

A modern, feature-rich desktop application built with Electron and React, showcasing best practices in cross-platform desktop app development.

![Electron React Dashboard](https://img.shields.io/badge/Electron-36.5.0-blue)
![React](https://img.shields.io/badge/React-19.1.0-blue)
![License](https://img.shields.io/badge/License-MIT-green)

## 🚀 Features

### 🎨 Modern UI/UX
- **Glassmorphism Design**: Beautiful translucent effects with backdrop blur
- **Responsive Layout**: Adapts to different window sizes and screen resolutions
- **Smooth Animations**: CSS transitions and keyframe animations
- **Dark/Light Theme Support**: Customizable appearance settings

### 📊 Dashboard & Analytics
- **Real-time Statistics**: Live updating counters and progress indicators
- **Activity Feed**: Track recent actions and system events
- **Quick Actions**: Fast access to common tasks
- **Progress Tracking**: Visual progress bars for projects and tasks

### 📁 Project Management
- **Project Creation**: Easy project setup with templates
- **Status Tracking**: Monitor project progress and deadlines
- **Team Management**: Assign team members to projects
- **Priority System**: High, Medium, Low priority classification

### ⚙️ Settings & Configuration
- **User Profile Management**: Customize user information and preferences
- **Application Settings**: Theme, language, notifications, and more
- **Data Export/Import**: Backup and restore application data
- **Performance Options**: Cache management and optimization

### 🔧 Advanced Features
- **Local Data Storage**: Persistent data using localStorage
- **Keyboard Shortcuts**: Global shortcuts for quick navigation
- **Menu Integration**: Native application menus with shortcuts
- **Cross-platform**: Runs on Windows, macOS, and Linux

## 🛠️ Technology Stack

### Frontend
- **React 19**: Modern UI library with hooks and concurrent features
- **React Router**: Client-side routing and navigation
- **CSS3**: Modern styling with flexbox, grid, and animations
- **JavaScript ES6+**: Modern JavaScript features

### Desktop
- **Electron 36.5.0**: Cross-platform desktop app framework
- **Node.js**: JavaScript runtime for desktop integration
- **Native APIs**: OS-level integrations and features

### Build Tools
- **Webpack 5**: Module bundling and optimization
- **Babel**: JavaScript transpilation for compatibility
- **CSS Loader**: CSS processing and injection
- **Electron Builder**: Application packaging and distribution

## 📦 Installation

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd electron_starter

# Install dependencies
npm install

# Build the application
npm run webpack

# Start the development version
npm start

# Build for production
npm run build
```

## 🎯 Usage

### Navigation
- Use the sidebar to navigate between different sections
- Click the hamburger menu (☰) to toggle the sidebar
- Use keyboard shortcuts for quick navigation:
  - `Ctrl+Shift+D`: Dashboard
  - `Ctrl+Shift+P`: Projects
  - `Ctrl+Shift+S`: Settings

### Project Management
1. Navigate to the Projects section
2. Click "New Project" to create a project
3. Fill in project details and save
4. Track progress and manage team assignments

### Settings Configuration
1. Go to Settings from the sidebar
2. Update user profile information
3. Customize application preferences
4. Export/import data as needed

## 🔧 Development

### Project Structure
```
electron_starter/
├── src/
│   ├── components/          # React components
│   │   ├── Header.js       # Application header
│   │   ├── Sidebar.js      # Navigation sidebar
│   │   ├── Dashboard.js    # Main dashboard
│   │   ├── Projects.js     # Project management
│   │   ├── Settings.js     # Settings panel
│   │   └── About.js        # About page
│   ├── styles/             # CSS stylesheets
│   │   └── App.css         # Main application styles
│   ├── utils/              # Utility functions
│   │   └── dataManager.js  # Data management system
│   ├── App.js              # Main React component
│   ├── renderer.js         # Renderer process entry
│   └── index.js            # Main Electron process
├── public/
│   └── index.html          # HTML template
├── build/                  # Compiled assets
├── dist/                   # Distribution builds
├── package.json            # Project configuration
└── webpack.config.js       # Webpack configuration
```

### Available Scripts
- `npm start`: Start the Electron application
- `npm run webpack`: Build the React application
- `npm run dev`: Build and start in development mode
- `npm run build`: Create production build

### Adding New Features
1. Create new components in `src/components/`
2. Add routes in `src/App.js`
3. Update navigation in `src/components/Sidebar.js`
4. Add styles in `src/styles/App.css`

## 🎨 Customization

### Themes
The application supports theme customization through the settings panel. You can modify the CSS variables in `App.css` to create custom themes.

### Components
All components are modular and can be easily customized or replaced. Each component follows React best practices with hooks and functional components.

### Data Management
The `dataManager` utility provides a complete data management system with:
- Local storage persistence
- CRUD operations for projects
- Activity tracking
- Settings management
- Data export/import

## 🚀 Building for Production

### Windows
```bash
npm run build
```
This creates a Windows executable in the `dist/` folder.

### Cross-platform
Configure `electron-builder` in `package.json` for other platforms:
- macOS: Add `mac` configuration
- Linux: Add `linux` configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Electron team for the amazing framework
- React team for the powerful UI library
- All contributors and the open-source community

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the code examples

---

Built with ❤️ using modern web technologies.
