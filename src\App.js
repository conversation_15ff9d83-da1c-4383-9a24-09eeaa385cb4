import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useNavigate } from 'react-router-dom';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import Projects from './components/Projects';
import Settings from './components/Settings';
import About from './components/About';
import dataManager from './utils/dataManager';
import './styles/App.css';

// Placeholder components for other routes
const Analytics = () => (
  <div className="fade-in">
    <h1>Analytics 📈</h1>
    <div className="card">
      <h2>Coming Soon</h2>
      <p>Advanced analytics and reporting features will be available here.</p>
    </div>
  </div>
);

const Tasks = () => (
  <div className="fade-in">
    <h1>Tasks ✅</h1>
    <div className="card">
      <h2>Task Management</h2>
      <p>Comprehensive task tracking and management system coming soon.</p>
    </div>
  </div>
);

const Calendar = () => (
  <div className="fade-in">
    <h1>Calendar 📅</h1>
    <div className="card">
      <h2>Schedule & Events</h2>
      <p>Integrated calendar with scheduling features coming soon.</p>
    </div>
  </div>
);

const Messages = () => (
  <div className="fade-in">
    <h1>Messages 💬</h1>
    <div className="card">
      <h2>Communication Hub</h2>
      <p>Team messaging and communication features coming soon.</p>
    </div>
  </div>
);

// Main App wrapper component
function AppContent() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const navigate = useNavigate();

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  // Handle Electron menu navigation events
  useEffect(() => {
    const handleElectronNavigate = (event) => {
      navigate(event.detail);
    };

    const handleNewProject = () => {
      navigate('/projects');
      // Could also trigger a modal or specific action
    };

    const handleOpenProject = () => {
      // Handle project opening logic
      console.log('Open project triggered from menu');
    };

    window.addEventListener('electron-navigate', handleElectronNavigate);
    window.addEventListener('electron-new-project', handleNewProject);
    window.addEventListener('electron-open-project', handleOpenProject);

    return () => {
      window.removeEventListener('electron-navigate', handleElectronNavigate);
      window.removeEventListener('electron-new-project', handleNewProject);
      window.removeEventListener('electron-open-project', handleOpenProject);
    };
  }, [navigate]);

  // Initialize data manager
  useEffect(() => {
    dataManager.addActivity('Application started', 'system');
  }, []);

  return (
    <div className="app">
      <Header onMenuToggle={toggleSidebar} />
      <Sidebar isOpen={sidebarOpen} onClose={closeSidebar} />

      <main className="main-content">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/analytics" element={<Analytics />} />
          <Route path="/projects" element={<Projects />} />
          <Route path="/tasks" element={<Tasks />} />
          <Route path="/calendar" element={<Calendar />} />
          <Route path="/messages" element={<Messages />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/about" element={<About />} />
        </Routes>
      </main>
    </div>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;