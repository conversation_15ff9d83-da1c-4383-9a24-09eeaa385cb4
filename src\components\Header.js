import React, { useState } from 'react';

const Header = ({ onMenuToggle }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every second
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <header className="header">
      <div className="header-left">
        <button 
          className="btn btn-secondary menu-toggle"
          onClick={onMenuToggle}
          style={{ marginRight: '1rem' }}
        >
          ☰
        </button>
        <h1>Electron React Dashboard</h1>
      </div>
      
      <div className="header-actions">
        <div className="time-display" style={{ textAlign: 'right', marginRight: '1rem' }}>
          <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#667eea' }}>
            {formatTime(currentTime)}
          </div>
          <div style={{ fontSize: '0.8rem', color: '#718096' }}>
            {formatDate(currentTime)}
          </div>
        </div>
        
        <div className="user-info" style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <div 
            style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: '600',
              fontSize: '0.9rem'
            }}
          >
            U
          </div>
          <span style={{ color: '#4a5568', fontSize: '0.9rem' }}>User</span>
        </div>
      </div>
    </header>
  );
};

export default Header;
