{"name": "electron_starter", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"electron": "^36.5.0", "electron-builder": "^26.0.12", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "babel-loader": "^10.0.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}}