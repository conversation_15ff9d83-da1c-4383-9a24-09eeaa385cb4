{"name": "electron-react-app", "version": "1.0.0", "description": "Simple Electron React App", "main": "src/index.js", "scripts": {"start": "electron .", "webpack": "webpack --config webpack.config.js", "dev": "npm run webpack && npm start", "build": "npm run webpack && electron-builder"}, "build": {"appId": "com.example.electron-react", "productName": "Electron React App", "directories": {"output": "dist"}, "win": {"target": "nsis"}, "files": ["build/**/*", "public/**/*", "src/index.js", "package.json"]}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "babel-loader": "^10.0.0", "electron": "^36.5.0", "electron-builder": "^26.0.12", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}}