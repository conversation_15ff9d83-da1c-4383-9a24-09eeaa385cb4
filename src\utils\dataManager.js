// Data Management Utility for Local Storage and State Management

class DataManager {
  constructor() {
    this.storagePrefix = 'electronReactApp_';
    this.initializeDefaultData();
  }

  // Initialize default data if not exists
  initializeDefaultData() {
    if (!this.getData('initialized')) {
      this.setData('initialized', true);
      this.setData('projects', this.getDefaultProjects());
      this.setData('settings', this.getDefaultSettings());
      this.setData('userProfile', this.getDefaultUserProfile());
      this.setData('activities', this.getDefaultActivities());
    }
  }

  // Generic data storage methods
  setData(key, data) {
    try {
      localStorage.setItem(this.storagePrefix + key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Error saving data:', error);
      return false;
    }
  }

  getData(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(this.storagePrefix + key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('Error loading data:', error);
      return defaultValue;
    }
  }

  removeData(key) {
    try {
      localStorage.removeItem(this.storagePrefix + key);
      return true;
    } catch (error) {
      console.error('Error removing data:', error);
      return false;
    }
  }

  clearAllData() {
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(this.storagePrefix)) {
          localStorage.removeItem(key);
        }
      });
      return true;
    } catch (error) {
      console.error('Error clearing data:', error);
      return false;
    }
  }

  // Project management methods
  getProjects() {
    return this.getData('projects', []);
  }

  addProject(project) {
    const projects = this.getProjects();
    const newProject = {
      id: Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...project
    };
    projects.push(newProject);
    this.setData('projects', projects);
    this.addActivity(`New project created: ${project.name}`, 'project');
    return newProject;
  }

  updateProject(id, updates) {
    const projects = this.getProjects();
    const index = projects.findIndex(p => p.id === id);
    if (index !== -1) {
      projects[index] = {
        ...projects[index],
        ...updates,
        updatedAt: new Date().toISOString()
      };
      this.setData('projects', projects);
      this.addActivity(`Project updated: ${projects[index].name}`, 'project');
      return projects[index];
    }
    return null;
  }

  deleteProject(id) {
    const projects = this.getProjects();
    const project = projects.find(p => p.id === id);
    if (project) {
      const filteredProjects = projects.filter(p => p.id !== id);
      this.setData('projects', filteredProjects);
      this.addActivity(`Project deleted: ${project.name}`, 'project');
      return true;
    }
    return false;
  }

  // Activity tracking
  getActivities() {
    return this.getData('activities', []);
  }

  addActivity(action, type = 'general', metadata = {}) {
    const activities = this.getActivities();
    const newActivity = {
      id: Date.now(),
      action,
      type,
      timestamp: new Date().toISOString(),
      metadata
    };
    activities.unshift(newActivity); // Add to beginning
    
    // Keep only last 100 activities
    if (activities.length > 100) {
      activities.splice(100);
    }
    
    this.setData('activities', activities);
    return newActivity;
  }

  // Settings management
  getSettings() {
    return this.getData('settings', this.getDefaultSettings());
  }

  updateSettings(updates) {
    const currentSettings = this.getSettings();
    const newSettings = { ...currentSettings, ...updates };
    this.setData('settings', newSettings);
    this.addActivity('Settings updated', 'settings');
    return newSettings;
  }

  // User profile management
  getUserProfile() {
    return this.getData('userProfile', this.getDefaultUserProfile());
  }

  updateUserProfile(updates) {
    const currentProfile = this.getUserProfile();
    const newProfile = { ...currentProfile, ...updates };
    this.setData('userProfile', newProfile);
    this.addActivity('Profile updated', 'user');
    return newProfile;
  }

  // Statistics and analytics
  getStatistics() {
    const projects = this.getProjects();
    const activities = this.getActivities();
    
    return {
      totalProjects: projects.length,
      activeProjects: projects.filter(p => p.status === 'In Progress').length,
      completedProjects: projects.filter(p => p.status === 'Completed').length,
      averageProgress: projects.length > 0 
        ? Math.round(projects.reduce((acc, p) => acc + (p.progress || 0), 0) / projects.length)
        : 0,
      recentActivities: activities.slice(0, 10),
      projectsByStatus: this.groupProjectsByStatus(projects),
      activitiesByType: this.groupActivitiesByType(activities)
    };
  }

  groupProjectsByStatus(projects) {
    return projects.reduce((acc, project) => {
      acc[project.status] = (acc[project.status] || 0) + 1;
      return acc;
    }, {});
  }

  groupActivitiesByType(activities) {
    return activities.reduce((acc, activity) => {
      acc[activity.type] = (acc[activity.type] || 0) + 1;
      return acc;
    }, {});
  }

  // Export/Import functionality
  exportData() {
    const data = {
      projects: this.getProjects(),
      settings: this.getSettings(),
      userProfile: this.getUserProfile(),
      activities: this.getActivities(),
      exportDate: new Date().toISOString()
    };
    return data;
  }

  importData(data) {
    try {
      if (data.projects) this.setData('projects', data.projects);
      if (data.settings) this.setData('settings', data.settings);
      if (data.userProfile) this.setData('userProfile', data.userProfile);
      if (data.activities) this.setData('activities', data.activities);
      
      this.addActivity('Data imported successfully', 'system');
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  // Default data generators
  getDefaultProjects() {
    return [
      {
        id: 1,
        name: 'Website Redesign',
        description: 'Complete overhaul of the company website with modern design',
        status: 'In Progress',
        progress: 75,
        team: ['John', 'Sarah', 'Mike'],
        deadline: '2024-02-15',
        priority: 'High',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 2,
        name: 'Mobile App Development',
        description: 'Native mobile application for iOS and Android platforms',
        status: 'Planning',
        progress: 25,
        team: ['Alice', 'Bob'],
        deadline: '2024-03-30',
        priority: 'Medium',
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  }

  getDefaultSettings() {
    return {
      theme: 'light',
      notifications: true,
      autoSave: true,
      language: 'en',
      fontSize: 'medium',
      showTips: true,
      autoBackup: false,
      soundEnabled: true
    };
  }

  getDefaultUserProfile() {
    return {
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'Administrator',
      avatar: '',
      joinDate: new Date().toISOString(),
      preferences: {
        dashboardLayout: 'grid',
        defaultView: 'dashboard'
      }
    };
  }

  getDefaultActivities() {
    return [
      {
        id: 1,
        action: 'Application initialized',
        type: 'system',
        timestamp: new Date().toISOString(),
        metadata: {}
      }
    ];
  }
}

// Create singleton instance
const dataManager = new DataManager();

export default dataManager;
