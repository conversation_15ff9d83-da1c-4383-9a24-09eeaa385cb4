<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Electron React Dashboard</title>
    <meta name="description" content="A modern desktop application built with Electron and React" />
    <style>
      /* Loading screen styles */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      }

      .loading-content {
        text-align: center;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem auto;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- Loading screen -->
    <div id="loading">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <h2>Loading Dashboard...</h2>
        <p>Please wait while we prepare your workspace</p>
      </div>
    </div>

    <!-- Main app container -->
    <div id="root"></div>

    <!-- App bundle -->
    <script src="../build/bundle.js"></script>

    <!-- Hide loading screen when app is ready -->
    <script>
      window.addEventListener('DOMContentLoaded', () => {
        // Hide loading screen after a short delay to ensure smooth transition
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            loading.style.transition = 'opacity 0.5s ease';
            setTimeout(() => {
              loading.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>